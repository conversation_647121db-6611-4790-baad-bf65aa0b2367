using Domain.Entities;
using Domain.EnumsModel;
using Domain.IRepo;
using Domain.IServices;
using Domain.ModelDTO;
using Microsoft.AspNetCore.Identity;

namespace Application;

public class OrderServices : IOrderServices
{
    private readonly IOrderRepository _orderRepository;
    private readonly IOrderApprovalRepository _orderApprovalRepository;
    private readonly ISettingRepository _settingRepository;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly IFcmService _fcmService;

    public OrderServices(IOrderRepository orderRepository,
        IOrderApprovalRepository orderApprovalRepository,
        ISettingRepository settingRepository,
        UserManager<ApplicationUser> userManager,
        IFcmService fcmService)
    {
        _orderRepository = orderRepository;
        _orderApprovalRepository = orderApprovalRepository;
        _settingRepository = settingRepository;
        _userManager = userManager;
        _fcmService = fcmService;
    }

    public IEnumerable<Order> GetAll()
    {
        return _orderRepository.GetAll();
    }

    public IEnumerable<OrderDto> GetOrders()
    {
        return _orderRepository.GetAll()
            .Select(order => new OrderDto
            {
                Id = order.Id,
                ClientId = order.ClientId,
                PickupLocation = order.PickupLocation,
                PickupCoordinates = order.PickupCoordinates,
                DeliveryLocation = order.DeliveryLocation,
                DeliveryCoordinates = order.DeliveryCoordinates,
                Destination = order.Destination,
                Price = order.Price,
                Description = order.Description,
                OrderApproval = order.OrderApproval != null
                    ? new OrderApprovalDto
                    {
                        OrderId = order.Id,
                        DriverId = order.OrderApproval.DriverId,
                        DriverName = order.OrderApproval.Driver.FirstNameEn,
                        IsApproved = order.OrderApproval.IsApproved,
                        ApprovalDate = order.OrderApproval.ApprovalDate,
                    }
                    : null,
                Status = order.Status.ToString(),
                Transport = new TransportDto
                {
                    Id = order.Transport.Id,
                    Type = order.Transport.Type,
                    Image = order.Transport.Image
                },
            });
    }

    public Order GetById(int id)
    {
        return _orderRepository.Get(id);
    }

    public OrderDto GetOrder(int id)
    {
        var order = _orderRepository.GetAll().FirstOrDefault(x => x.Id == id);

        if (order == null)
            return null!; // Handle case where order is not found

        return new OrderDto
        {
            Id = order.Id,
            ClientId = order.ClientId,
            PickupLocation = order.PickupLocation,
            PickupCoordinates = order.PickupCoordinates,
            DeliveryLocation = order.DeliveryLocation,
            DeliveryCoordinates = order.DeliveryCoordinates,
            Destination = order.Destination,
            Price = order.Price,
            Description = order.Description,
            OrderApproval = order.OrderApproval != null
                ? new OrderApprovalDto
                {
                    OrderId = order.Id,
                    DriverId = order.OrderApproval.DriverId,
                    DriverName = order.OrderApproval.Driver.FirstNameEn,
                    IsApproved = order.OrderApproval.IsApproved,
                    ApprovalDate = order.OrderApproval.ApprovalDate,
                }
                : null,
            Status = order.Status.ToString(),
            Transport = new TransportDto
            {
                Id = order.Transport.Id,
                Type = order.Transport.Type,
                Image = order.Transport.Image
            }
        };
    }

    public void Add(Order entity)
    {
        entity.CreatedDate = DateTime.UtcNow;
        _orderRepository.Add(entity);
        if (entity.Id == 0) return;

        var setting = _settingRepository.GetAll().FirstOrDefault();
        var requiredDistance = setting?.RequiredDistance ?? 0;

        // Send notification to all drivers via topic asynchronously without blocking
        _ = Task.Run(async () =>
        {
            try
            {
                await _fcmService.SendNotificationAsync(
                    null!, // No specific device token
                    "لديك طلب جديد",
                    "طلب توصيل جديد للموقع : " + entity.PickupLocation + " الى الموقع : " + entity.DeliveryLocation,
                    true, // isSilent
                    "driver", // topic
                    new
                    {
                        TripCost = entity.Price,
                        TripDate = entity.CreatedDate,
                        RequiredDistance = requiredDistance,
                        action = "new_order",
                        id = entity.Id,
                        priority = "high",
                        delivery_location = entity.DeliveryLocation,
                        delivery_coordinates = entity.DeliveryCoordinates,
                        pickup_location = entity.PickupLocation,
                        pickup_coordinates = entity.PickupCoordinates,
                    }
                );
            }
            catch (Exception ex)
            {
                // Log the error but don't throw to avoid breaking the main flow
                Console.WriteLine($"FCM notification error: {ex.Message}");
            }
        });
    }

    public void Update(Order entity)
    {
        _orderRepository.Update(entity);
    }

    public void Delete(Order entity)
    {
        _orderRepository.Delete(entity);
    }

    public void Delete(int id)
    {
        _orderRepository.Delete(id);
    }

    public IQueryable<Order> GetAllQuery(Guid userId)
    {
        throw new NotImplementedException();
    }

    public IEnumerable<OrderDto> GetOrdersByClientId(string clientId)
    {
        return _orderRepository.GetAll()
            .Where(x => x.ClientId == clientId)
            .Select(order => new OrderDto
            {
                Id = order.Id,
                ClientId = order.ClientId,
                PickupLocation = order.PickupLocation,
                PickupCoordinates = order.PickupCoordinates,
                DeliveryLocation = order.DeliveryLocation,
                DeliveryCoordinates = order.DeliveryCoordinates,
                Destination = order.Destination,
                Price = order.Price,
                Description = order.Description,
                OrderApproval = order.OrderApproval != null
                    ? new OrderApprovalDto
                    {
                        OrderId = order.Id,
                        DriverId = order.OrderApproval.DriverId,
                        DriverName = order.OrderApproval.Driver.FirstNameEn,
                        IsApproved = order.OrderApproval.IsApproved,
                        ApprovalDate = order.OrderApproval.ApprovalDate,
                    }
                    : null,
                Status = order.Status.ToString(),
                Transport = new TransportDto
                {
                    Id = order.Transport.Id,
                    Type = order.Transport.Type,
                    Image = order.Transport.Image
                },
            });
    }

    public Order UpdateStatus(int id, OrderStatus status)
    {
        var order = _orderRepository.Get(id);
        // ReSharper disable once ConditionIsAlwaysTrueOrFalseAccordingToNullableAPIContract
        if (order == null)
            return null!;

        order.Status = status;
        _orderRepository.Update(order);
        var client = _userManager.Users.FirstOrDefault(x => x.Id == order.ClientId);

        // Send notification asynchronously without blocking
        if (client?.device_token != null && order.OrderApproval?.Driver.UserName != null)
        {
            _ = Task.Run(async () =>
            {
                try
                {
                    await _fcmService.SendNotificationAsync(
                        client.device_token,
                        $" {status}حالة الطلب ",
                        "اسم الطيار :" + order.OrderApproval.Driver.UserName
                    );
                }
                catch (Exception ex)
                {
                    // Log the error but don't throw to avoid breaking the main flow
                    Console.WriteLine($"FCM notification error: {ex.Message}");
                }
            });
        }

        return order;
    }

    public double? GetTotal(double distance)
    {
        var price = _settingRepository.GetAll().FirstOrDefault()?.Price ?? 0;
        return distance * price;
    }

    // Approve Order
    public bool ApproveOrder(int orderId, string driverId)
    {
        var order = _orderRepository.Get(orderId);
        // ReSharper disable once ConditionIsAlwaysTrueOrFalseAccordingToNullableAPIContract
        if (order == null) return false;

        var driver = _userManager.Users.FirstOrDefault(u => u.Id == driverId);
        if (driver == null) return false;

        var approval = new OrderApproval
        {
            OrderId = orderId,
            DriverId = driverId,
            IsApproved = true,
            ApprovalDate = DateTime.UtcNow,
            Driver = driver
        };

        _orderApprovalRepository.Add(approval);
        order.Status = OrderStatus.Approved;
        _orderRepository.Update(order);
        var client = _userManager.Users.FirstOrDefault(x => x.Id == order.ClientId);

        // Send notification asynchronously without blocking
        if (client?.device_token != null && driver.UserName != null)
        {
            _ = Task.Run(async () =>
            {
                try
                {
                    await _fcmService.SendNotificationAsync(
                        client.device_token,
                        "تم الموافقه علي طلبك",
                        "اسم الطيار :" + driver.UserName
                    );
                }
                catch (Exception ex)
                {
                    // Log the error but don't throw to avoid breaking the main flow
                    Console.WriteLine($"FCM notification error: {ex.Message}");
                }
            });
        }

        return true;
    }

    public IEnumerable<OrderApprovalDto> GetApproveOrders()
    {
        return _orderApprovalRepository.GetAll()
            .Select(o => new OrderApprovalDto
            {
                OrderId = o.OrderId,
                Order = new OrderDto
                {
                    ClientId = o.Order.ClientId,
                    Description = o.Order.Description,
                    PickupLocation = o.Order.PickupLocation,
                    DeliveryLocation = o.Order.DeliveryLocation,
                    Price = o.Order.Price,
                    Status = o.Order.Status.ToString(),
                    // Transport =new TransportDto
                    // {
                    //     Id = o.Order.Transport.Id,
                    //     Type = o.Order.Transport.Type,
                    //     Image = o.Order.Transport.Image
                    // },
                },
                DriverId = o.DriverId,
                DriverName =
                    o.Driver.FirstNameEn + " " +
                    o.Driver.lastNameEn,
                IsApproved = o.IsApproved,
                ApprovalDate = o.ApprovalDate
            });
    }

    public IEnumerable<OrderApprovalDto> GetApproveOrdersByDriverId(string driverId, string state)
    {
        return _orderApprovalRepository.GetAll()
            .Where(x => x.DriverId == driverId)
            .Select(o => new OrderApprovalDto
            {
                OrderId = o.OrderId,
                Order = new OrderDto
                {
                    ClientId = o.Order.ClientId,
                    Description = o.Order.Description,
                    PickupLocation = o.Order.PickupLocation,
                    DeliveryLocation = o.Order.DeliveryLocation,
                    Price = o.Order.Price,
                    Status = o.Order.Status.ToString(),
                },
                DriverId = o.DriverId,
                DriverName = o.Driver.FirstNameEn + " " + o.Driver.lastNameEn,
                IsApproved = o.IsApproved,
                ApprovalDate = o.ApprovalDate
            })
            .Where(dto =>
                state == "all" ||
                (state == "approve" && dto.IsApproved) ||
                (state == "canceled" && !dto.IsApproved));
    }
}