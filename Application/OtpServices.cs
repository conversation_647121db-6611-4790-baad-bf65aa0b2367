using Domain.Entities;
using Domain.IRepo;
using Domain.IServices;
using Domain.ModelDTO;
using Microsoft.Extensions.Configuration;
using Nancy.Json;
using System.Net;
using Domain.Helper;
using Microsoft.AspNetCore.Identity;

namespace Application
{
    public class OtpServices(IConfiguration configure, IOtpRepository otpRepository, IFcmService fcmService,UserManager<ApplicationUser> userManager)
        : IOtpServices
    {
        public async void Add(Otp entity)
        {
            const string info = "Your Verification Code: "; // information
            var verificationCode = new int[4];
            var random = new Random();
            var codeGenerator = new int[4];

            // generates verification code
            for (var i = 0; i < 4; i++)
            {
                codeGenerator[i] = random.Next(0, 9);
                verificationCode[i] = codeGenerator[i];
            }
            var code = info + string.Join("", verificationCode);
            entity.OtpNo = string.Join("", verificationCode);
            otpRepository.Add(entity);
            if (entity.Firebase != null)
            { 
                await fcmService.SendNotificationAsync(entity.Firebase, "Carry&Go", code);
            }
        }

        public void Delete(Otp entity)
        {
            throw new NotImplementedException();
        }

        public void Delete(int id)
        {
            throw new NotImplementedException();
        }

        public IEnumerable<Otp> GetAll()
        {
            throw new NotImplementedException();
        }

        public IQueryable<Otp> GetAllQuery(Guid userId)
        {
            throw new NotImplementedException();
        }

        public Otp GetById(int id)
        {
            throw new NotImplementedException();
        }

        public void Update(Otp entity)
        {
            throw new NotImplementedException();
        }

        [Obsolete("Obsolete")]
        public async Task<ResultDTO<Dictionary<string, dynamic>>> SendNotification(SendNotification notification)
        {
            var res = new Dictionary<string, dynamic>();
            //if (notification.FirebaseToken.Count == 0)
            //{
            //    notification.FirebaseToken.Add("/topics/All");
            //}
            //if (notification.FirebaseToken == null || notification.FirebaseToken[0] == "" || notification.FirebaseToken[0] == "string")
            //{
            //    notification.FirebaseToken[0] = "/topics/All";
            //}
            //if (notification.FirebaseToken == null || notification.FirebaseToken == "" || notification.FirebaseToken == "string")
            //{
            //    notification.FirebaseToken = "/topics/All";
            //}
            var notificationDto = new NotificationDTO()
            {
                title = notification.title,
                body = notification.body,
                content_available = "true",
                sound = "beep",
                //  click_action = "FLUTTER_NOTIFICATION_CLICK",
                image = notification.image,
                //icon=notification.image
            };
            string result;
            var expData = new NotificationTextDTO()
            {
                bodyText = notification.body,
                image = notification.image,
                //icon= notification.image
            };
            //  _logger.LogError(notification.FirebaseToken);
            //foreach (var item in notification.FirebaseToken)
            //{
                var data = new NotificationDataDTO()
                {
                    to = notification.FirebaseToken,
                   // to = item,
                    ttl = 3600,
                    notification = notificationDto,
                    data = expData
                };
                var httpWebRequest = (HttpWebRequest)WebRequest.Create("https://fcm.googleapis.com/fcm/send");
                httpWebRequest.ContentType = "application/json";
                httpWebRequest.Headers.Add($"Authorization: key={notification.ServerKey}");
                httpWebRequest.Headers.Add($"Sender: id={notification.SenderId}");
                httpWebRequest.Method = "POST";
                var serializer = new JavaScriptSerializer();
                await using (var streamWriter = new StreamWriter(httpWebRequest.GetRequestStream()))
                {
                    var json = serializer.Serialize(data);
                    await streamWriter.WriteAsync(json);
                    await streamWriter.FlushAsync();
                }
                var httpResponse = (HttpWebResponse)httpWebRequest.GetResponse();
                using (var streamReader = new StreamReader(httpResponse.GetResponseStream()))
                {
                    result = await streamReader.ReadToEndAsync();
                }

            //}
            res.Add("Message", result);
            var obj = new ResultDTO<Dictionary<string, dynamic>>()
            {
                Object = res,
                Message = "Done",
                State = true
            };
            return obj;
        }

        public ResultMSG VerificationOtp(Otp otp)
        {
            var checkOtp = otpRepository.GetAllQuery().FirstOrDefault(r => r.OtpNo == otp.OtpNo && r.PhoneEmail == otp.PhoneEmail);
            if (checkOtp != null) {
                return new ResultMSG
                {
                    MessageAr = "الرمز صحيح",
                    MessageEn = "Otp Code Is Right",
                    Status = true
                };
            }
            else
            {
                return new ResultMSG
                {
                    MessageAr = "الرمز خطأ",
                    MessageEn = "Otp Code Is Wrong",
                    Status = false
                };
            }
        }

        public void SendOtpByEmail(string phone)
        {
            const string info = "Your Verification Code: "; // information
            var verificationCode = new int[4];
            var random = new Random();
            var codeGenerator = new int[4];

            // generates verification code
            for (var i = 0; i < 4; i++)
            {
                codeGenerator[i] = random.Next(0, 9);
                verificationCode[i] = codeGenerator[i];
            }
            var code = info + string.Join("", verificationCode);
            var entity = new Otp
            {
                OtpNo = string.Join("",
                    verificationCode),
                PhoneEmail = phone
            };
            otpRepository.Add(entity);
            var email = userManager.Users.FirstOrDefault(x=>x.PhoneNumber==phone)!.Email;
            var item = new EmailSender.EmailOtp
            {
                Email = email,
                Otp = code
            };
            var emailSender = new EmailSender(configuration:configure);
            emailSender.Sender(item);
        }
    }
}
