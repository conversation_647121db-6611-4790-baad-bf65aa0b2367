using Domain.BasicModels;
using Domain.Entities;
using Domain.IRepo;
using Domain.IServices;
using Domain.ModelDTO;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Net;
using System.Net.Mail;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Microsoft.EntityFrameworkCore;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using System.Text.RegularExpressions;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using Domain.Helper;
using MimeKit;

namespace Application
{
    public class UserServices : IUserServices
    {
        private readonly IDriverLicenseRepository _driverLicenseRepository;
        private readonly ICarInfoRepository _carInfoRepository;
        private readonly IUserDetailsInfoRepository _userDetailsInfoRepository;
        private readonly ICompanyInfoRepository _companyInfoRepository;

        private readonly UserManager<ApplicationUser> _userManager;

        //private readonly RoleManager<IdentityRole> _roleManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly IConfiguration _configuration;
        private readonly ILogger<UserServices> _logger;
        private readonly IMapper _mapper;

        public UserServices(
            ICarInfoRepository carInfoRepository,
            IDriverLicenseRepository driverLicenseRepository,
            IUserDetailsInfoRepository userDetailsInfoRepository,
            ICompanyInfoRepository companyInfoRepository,
            UserManager<ApplicationUser> userManager,
            ILogger<UserServices> logger,
            //RoleManager<IdentityRole> roleManager,
            IConfiguration configuration,
            SignInManager<ApplicationUser> signInManager,
            IMapper mapper
        )
        {
            _logger = logger;
            _userManager = userManager;
            //_roleManager = roleManager;
            _configuration = configuration;
            _signInManager = signInManager;
            _driverLicenseRepository = driverLicenseRepository;
            _carInfoRepository = carInfoRepository;
            _mapper = mapper;
            _userDetailsInfoRepository = userDetailsInfoRepository;
            _companyInfoRepository = companyInfoRepository;
        }

        public async Task<RegistrationRes> RegisterClient(UserDto user)
        {
            try
            {
                const string passwordComp = "123456aA@";
                var email = user?.Email!.Trim();
                var phoneNumber = user?.Mobile.Trim();

                // Check if the user already by email exists
                var existingUser =
                    await _userManager.Users.FirstOrDefaultAsync(r => r.UserName == email && !r.isDeleted);

                if (existingUser != null)
                {
                    const string errorMessage = "This account already exists. Please use another email.";
                    _logger.LogWarning(errorMessage);
                    return new RegistrationRes
                    {
                        Result = false,
                        Errors = [errorMessage]
                    };
                }

                // Check if the user already by email exists
                var existingUserByPhone = await _userManager.Users
                    .FirstOrDefaultAsync(r => r.PhoneNumber == phoneNumber && !r.isDeleted);

                if (existingUserByPhone != null)
                {
                    const string errorMessage = "This account already exists. Please use another phone number.";
                    _logger.LogWarning(errorMessage);
                    return new RegistrationRes
                    {
                        Result = false,
                        Errors = [errorMessage]
                    };
                }

                // Create a new user
                var newUser = new ApplicationUser
                {
                    ExternalEmail = email,
                    PhoneNumber = phoneNumber,
                    UserName = email,
                    UsernameEn = email,
                    UsernameAr = email,
                    FirstNameEn = user!.FirstName.Trim(),
                    lastNameEn = user.LastName.Trim(),
                    FirstNameAr = user.FirstName.Trim(),
                    lastNameAr = user.LastName.Trim(),
                    Email = email,
                    UserType = user.UserType,
                    isDeleted = false,
                    Image = string.Empty,
                    CreatedBy = $"{user.FirstName.Trim()} {user.LastName.Trim()}",
                    LastEditeBy = string.Empty,
                    device_token = string.Empty,
                    fingerPrintIdAndroid = string.Empty,
                    path = string.Empty,
                    ClientType = user.ClientType
                };

                // Create the user with a custom password
                var createUserResult = await _userManager.CreateAsync(newUser, user.Password + passwordComp);

                if (!createUserResult.Succeeded)
                {
                    return new RegistrationRes
                    {
                        Result = false,
                        Errors = createUserResult.Errors.Select(e => e.Description).ToList()
                    };
                }

                // Retrieve the created user
                var myUser = await _userManager.FindByEmailAsync(newUser.Email!);
                if (user.ClientType == 2)
                {
                    // Map and save UserDetailsInfo
                    var infoComp = new Company
                    {
                        Name = user.ClientInfo!.Name,
                        Address = user.ClientInfo!.Address,
                        Description = user.ClientInfo.Description,
                        CommercialRegisterImg = user.ClientInfo.CommercialRegisterImg,
                        ClientId = myUser!.Id,
                        CreatedDate = DateTime.Now
                    };
                    _companyInfoRepository.Add(infoComp);
                }

                // Generate JWT token and return success response
                var roles = await _userManager.GetRolesAsync(myUser!);
                var jwtToken = GenerateJwtToken(newUser);
                _logger.LogInformation("An account has been created.");

                return new RegistrationRes
                {
                    Result = true,
                    Token = jwtToken,
                    Id = myUser!.Id,
                    UserName = myUser.UserName,
                    RolesName = roles.ToList(),
                    Type = myUser.UserType,
                    ClientType = myUser.ClientType,
                    Data = new Dictionary<string, object>()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while registering the user.");
                throw; // Consider handling specific exceptions for better error messages
            }
        }

        public async Task<RegistrationRes> Register(DriverDto user)
        {
            try
            {
                const string passwordComp = "123456aA@";
                var email = user.DriverInfo?.Email!.Trim();
                var phoneNumber = user.DriverInfo?.Mobile.Trim();

                // Check if the user already by email exists
                var existingUser =
                    await _userManager.Users.FirstOrDefaultAsync(r => r.UserName == email && !r.isDeleted);

                if (existingUser != null)
                {
                    const string errorMessage = "This account already exists. Please use another email.";
                    _logger.LogWarning(errorMessage);
                    return new RegistrationRes
                    {
                        Result = false,
                        Errors = [errorMessage]
                    };
                }

                // Check if the user already by email exists
                var existingUserByPhone = await _userManager.Users
                    .FirstOrDefaultAsync(r => r.PhoneNumber == phoneNumber && !r.isDeleted);

                if (existingUserByPhone != null)
                {
                    const string errorMessage = "This account already exists. Please use another phone number.";
                    _logger.LogWarning(errorMessage);
                    return new RegistrationRes
                    {
                        Result = false,
                        Errors = [errorMessage]
                    };
                }

                // Create a new user
                var newUser = new ApplicationUser
                {
                    ExternalEmail = email,
                    PhoneNumber = phoneNumber,
                    UserName = email,
                    UsernameEn = email,
                    UsernameAr = email,
                    FirstNameEn = user.DriverInfo!.FirstName.Trim(),
                    lastNameEn = user.DriverInfo!.LastName.Trim(),
                    FirstNameAr = user.DriverInfo.FirstName.Trim(),
                    lastNameAr = user.DriverInfo.LastName.Trim(),
                    Email = email,
                    UserType = 2,
                    isDeleted = false,
                    Image = string.Empty,
                    CreatedBy = $"{user.DriverInfo.FirstName.Trim()} {user.DriverInfo.LastName.Trim()}",
                    LastEditeBy = string.Empty,
                    device_token = string.Empty,
                    fingerPrintIdAndroid = string.Empty,
                    path = string.Empty
                };

                // Create the user with a custom password
                var createUserResult = await _userManager.CreateAsync(newUser, user.DriverInfo.Password + passwordComp);

                if (!createUserResult.Succeeded)
                {
                    return new RegistrationRes
                    {
                        Result = false,
                        Errors = createUserResult.Errors.Select(e => e.Description).ToList()
                    };
                }

                // Retrieve the created user
                var myUser = await _userManager.FindByEmailAsync(newUser.Email!);

                // Map and save UserDetailsInfo
                var userDetails = _mapper.Map<UserDetailsInfo>(user.NationalInfo);
                if (userDetails != null)
                {
                    userDetails.UserId = myUser!.Id;
                    userDetails.CreatedDate = DateTime.Now;
                    _userDetailsInfoRepository.Add(userDetails);
                }

                // Map and save DriverLicense
                var driverLicense = _mapper.Map<DriverLicense>(user.DriverLicense);
                if (driverLicense != null)
                {
                    driverLicense.UserId = myUser!.Id;
                    driverLicense.DrCertGConductImage = user.DriverCertificateOfGoodConduct!.CertImage;
                    _driverLicenseRepository.Add(driverLicense);
                }

                // Map and save CarInfo
                var carInfo = _mapper.Map<CarInfo>(user.CarInfo);
                if (carInfo != null)
                {
                    carInfo.UserId = myUser!.Id;
                    carInfo.CarProductionYear = string.Empty;
                    carInfo.CrcarRegistrationImgB = string.Empty;
                    carInfo.CrcarRegistrationImgF = string.Empty;
                    carInfo.CarProductionYear = string.Empty;
                    _carInfoRepository.Add(carInfo);
                }

                // Generate JWT token and return success response
                var roles = await _userManager.GetRolesAsync(myUser!);
                var jwtToken = GenerateJwtToken(newUser);
                _logger.LogInformation("An account has been created.");

                return new RegistrationRes
                {
                    Result = true,
                    Token = jwtToken,
                    Id = myUser!.Id,
                    UserName = myUser.UserName,
                    Type = myUser.UserType,
                    RolesName = roles.ToList(),
                    Data = new Dictionary<string, object>()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while registering the user.");
                throw; // Consider handling specific exceptions for better error messages
            }
        }

        private string GenerateJwtToken(ApplicationUser user)
        {
            try
            {
                // Now its ime to define the jwt token which will be responsible of creating our tokens
                var jwtTokenHandler = new JwtSecurityTokenHandler();

                // We get our secret from the appsettings
                //var key = Encoding.ASCII.GetBytes("ijurkbdlhmklqacwqzdxmkkhvqowlyqa");
                var authSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["JWT:Secret"]));
                if (authSigningKey.KeySize < 512)
                {
                    throw new ArgumentOutOfRangeException("JWT:Secret", "Key size must be at least 512 bits.");
                }
                
                var tokenDescriptor = new SecurityTokenDescriptor
                {
                    Subject = new ClaimsIdentity(new[]
                    {
                        new Claim("Id", user.Id),
                        new Claim("UserName", user.UserName ?? string.Empty),
                        new Claim("PhoneNumber", user.PhoneNumber ?? string.Empty),
                        new Claim("Type", user.UserType.ToString()),
                        new Claim("ClientType", user.ClientType?.ToString() ?? string.Empty),
                        new Claim(JwtRegisteredClaimNames.Email, user.Email ?? string.Empty),
                        new Claim(ClaimTypes.NameIdentifier, user.Id),
                        // the JTI is used for our refresh token which we will be convering in the next video
                        new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
                    }),
                    // the life span of the token needs to be shorter and utilise refresh token to keep the user signedin
                    // but since this is a demo app we can extend it to fit our current need
                    Expires = DateTime.UtcNow.AddYears(2),
                    Issuer = _configuration["JWT:ValidIssuer"],
                    Audience = _configuration["JWT:ValidAudience"],
                    // here we are adding the encryption alogorithim information which will be used to decrypt our token
                    SigningCredentials = new SigningCredentials(authSigningKey, SecurityAlgorithms.HmacSha512Signature)
                };

                var token = jwtTokenHandler.CreateToken(tokenDescriptor);
                var jwtToken = jwtTokenHandler.WriteToken(token);
                return jwtToken;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
                throw ex;
            }
        }

        public async Task<RegistrationRes> ResetPassword(ResetPassword user)
        {
            var existingMobileUser = await _userManager.Users.FirstOrDefaultAsync(r => r.PhoneNumber == user.mobile);

            if (existingMobileUser == null)
            {
                const string errorMessage = "This mobile Not exist - Can't Reset Password ! ";

                _logger.LogWarning(errorMessage);
                return new RegistrationRes()
                {
                    Result = false,
                    Errors = [errorMessage]
                };
            }

            // if (existingMobileUser == null)
            // {
            //
            //     string ErrorMessage = "This account is not have username - the password cannot be reset!";
            //
            //     _logger.LogWarning(ErrorMessage);
            //     return new RegistrationRes()
            //     {
            //         Result = false,
            //         Errors = new List<string>()
            //         {
            //                 ErrorMessage
            //         },
            //     };
            // }
            var existingUser =
                await _userManager.Users.FirstOrDefaultAsync(r =>
                    r.PhoneNumber == user.mobile && r.UserName == user.username);

            if (existingUser == null)
            {
                const string errorMessage =
                    "This mobile is not associated with this username - the password cannot be reset!";

                _logger.LogWarning(errorMessage);
                return new RegistrationRes()
                {
                    Result = false,
                    Errors = [errorMessage],
                };
            }


            if (user.password != user.confirmpassword)
            {
                const string errorMessage =
                    "Check Password Again ! The password does not match the confirmed password - Can't Reset Password ! ";
                _logger.LogWarning(errorMessage);
                return new RegistrationRes()
                {
                    Result = false,
                    Errors = [errorMessage]
                };
            }

            existingUser.isDeleted = false;

            const string passwordComp = "123456aA@";
            var createCustomePass = user.password + passwordComp;
            var passwordHasher = new PasswordHasher<string>();
            var hashedPassword = passwordHasher.HashPassword(null!, createCustomePass);

            existingUser.PasswordHash = hashedPassword;

            var isUpdated = await _userManager.UpdateAsync(existingUser);
            switch (isUpdated.Succeeded)
            {
                case true:
                {
                    var myUser = await _userManager.FindByEmailAsync(existingUser.Email!);
                    //var rolename = await _userManager.GetRolesAsync(myUser!);
                    var jwtToken = GenerateJwtToken(existingUser);
                    _logger.LogWarning("an account Is Updated ");
                    return new RegistrationRes()
                    {
                        Result = true,
                        Token = jwtToken,
                        Id = myUser!.Id
                    };
                }
                default:
                    return new RegistrationRes()
                    {
                        Result = false,
                        Errors = isUpdated.Errors.Select(x => x.Description).ToList()
                    };
            }
        }

        static string GenerateRandomPassword(int length)
        {
            const string validChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890!@#$%^&*()";
            var password = new char[length];
            using (var rng = RandomNumberGenerator.Create())
            {
                var byteBuffer = new byte[length];
                rng.GetBytes(byteBuffer);
                for (var i = 0; i < length; i++)
                {
                    password[i] = validChars[byteBuffer[i] % validChars.Length];
                }
            }

            return new string(password);
        }

        public async Task<RegistrationRes> ForgetPassword(ForgetPassword user)
        {
            try
            {
                var existingMobileUser =
                    await _userManager.Users.FirstOrDefaultAsync(r => r.PhoneNumber == user.Mobile);

                if (existingMobileUser == null)
                {
                    const string errorMessage = "This mobile does not exist - Can't Reset Password!";
                    _logger.LogWarning(errorMessage);
                    return new RegistrationRes() { Result = false, Massage = errorMessage };
                }

                // Generate a stronger but simpler password (avoid complex password compositions that might cause issues)
                //var dynamicPassword = GenerateRandomPassword(8); // Increased from 7 to 8 for better security

                // Set the password directly without appending a fixed string (more secure)
                var passwordHasher = new PasswordHasher<ApplicationUser>(); // Use proper generic type
                var hashedPassword = passwordHasher.HashPassword(existingMobileUser, user.Password);

                existingMobileUser.PasswordHash = hashedPassword;
                var isUpdated = await _userManager.UpdateAsync(existingMobileUser);
                if (!isUpdated.Succeeded)
                {
                    _logger.LogError("Failed to update user password.");
                    return new RegistrationRes() { Result = false, Massage = "Failed to update password." };
                }

                var smtpSection = _configuration.GetSection("SmtpSettings");

                var smtpClient = new SmtpClient(smtpSection["Host"])
                {
                    Port = int.Parse(smtpSection["Port"]!),
                    EnableSsl = bool.Parse(smtpSection["EnableSsl"]!),
                    UseDefaultCredentials = false,
                    Credentials = new NetworkCredential(smtpSection["Username"], smtpSection["Password"]),
                    DeliveryMethod = SmtpDeliveryMethod.Network,
                    Timeout = 30000 // 30 seconds timeout
                };

                // Create HTML content for the email - simplified and optimized for email clients
                var htmlBody = $@"
                    <!DOCTYPE html PUBLIC ""-//W3C//DTD XHTML 1.0 Transitional//EN"" ""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"">
                    <html xmlns=""http://www.w3.org/1999/xhtml"">
                    <head>
                        <meta http-equiv=""Content-Type"" content=""text/html; charset=UTF-8"" />
                        <meta name=""viewport"" content=""width=device-width, initial-scale=1.0""/>
                        <title>Password Reset</title>
                    </head>
                    <body style=""margin: 0; padding: 0; font-family: Arial, sans-serif; line-height: 1.6; color: #333333;"">
                        <table border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"">
                            <tr>
                                <td bgcolor=""#ffffff"" align=""center"">
                                    <table border=""0"" cellpadding=""0"" cellspacing=""0"" width=""600"" style=""max-width: 600px;"">
                                        <tr>
                                            <td align=""center"" style=""padding: 20px 0;"">
                                                <h1 style=""color: #0066cc; margin: 0;"">Carry & Go</h1>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td bgcolor=""#f9f9f9"" style=""padding: 20px; border-radius: 5px; border-left: 4px solid #0066cc;"">
                                                <p>Hello {existingMobileUser.UserName},</p>
                                                <p>We received a request to reset your password for your Carry & Go account.</p>
                                                <p>Your temporary password is: <strong>{user.Password}</strong></p>
                                                <p>Please log in to your account and change this password immediately for security reasons.</p>
                                                <p>If you didn't request this password reset, please contact our support team immediately.</p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style=""padding: 20px; text-align: center; font-size: 14px; color: #666666;"">
                                                <p>This is an automated message, please do not reply directly to this email.</p>
                                                <p>&copy; {DateTime.Now.Year} Carry & Go. All rights reserved.</p>
                                                <p>
                                                    <a href=""#"" style=""color: #0066cc; text-decoration: none;"">Privacy Policy</a> | 
                                                    <a href=""#"" style=""color: #0066cc; text-decoration: none;"">Terms of Service</a>
                                                </p>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </body>
                    </html>";

                // Create an improved plain text version that's properly formatted
                string textBody = $@"Carry & Go - Password Reset

                Hello {existingMobileUser.UserName},

                We received a request to reset your password for your Carry & Go account.

                Your temporary password is: {user.Password}

                Please log in to your account and change this password immediately for security reasons.

                If you didn't request this password reset, please contact our support team immediately.

                --
                This is an automated message, please do not reply directly to this email.
                © {DateTime.Now.Year} Carry & Go. All rights reserved.
                https://carryandgo.com";

                // Add proper MIME alternative view setup for better email client compatibility
                var mailMessage = new MailMessage
                {
                    From = new MailAddress(smtpSection["Username"]!, "Carry & Go Support"),
                    Subject = "Carry & Go - Password Reset Notification",
                    IsBodyHtml = true
                };

                // Add the recipient
                mailMessage.To.Add(existingMobileUser.Email!);

                // Add the plain text version first (important for proper MIME structure)
                var plainTextView =
                    AlternateView.CreateAlternateViewFromString(textBody, System.Text.Encoding.UTF8, "text/plain");
                mailMessage.AlternateViews.Add(plainTextView);

                // Add the HTML version next
                var htmlView =
                    AlternateView.CreateAlternateViewFromString(htmlBody, System.Text.Encoding.UTF8, "text/html");
                mailMessage.AlternateViews.Add(htmlView);

                // Set the Body property as well for clients that don't properly handle AlternateViews
                mailMessage.Body = htmlBody;

                // Add essential anti-spam headers to improve inbox delivery
                mailMessage.Headers.Add("X-Mailer", "Microsoft Outlook");
                mailMessage.Headers.Add("X-Priority", "3"); // Normal priority (1=high can trigger spam filters)
                mailMessage.Headers.Add("Importance", "Normal");
                mailMessage.Headers.Add("X-Auto-Response-Suppress", "OOF, DR, RN, NRN, AutoReply");
                mailMessage.Headers.Add("X-MSMail-Priority", "Normal");

                // Add DKIM-Signature header placeholder - you'll need to implement actual DKIM signing
                // This is just to remind you that DKIM is important for email deliverability
                // mailMessage.Headers.Add("DKIM-Signature", "your DKIM signature");

                // Configure the Reply-To address to match the From address
                mailMessage.ReplyToList.Add(new MailAddress(smtpSection["Username"]!, "Carry & Go Support"));

                // Set a message ID with your domain to improve deliverability
                string messageId = $"<{Guid.NewGuid()}@{new MailAddress(smtpSection["Username"]!).Host}>";
                mailMessage.Headers.Add("Message-ID", messageId);
                // Add subject line with company name first (improves deliverability)
                mailMessage.Subject = "Carry & Go - Password Reset Notification";

                // Set specific content type with proper charset
                mailMessage.BodyEncoding = System.Text.Encoding.UTF8;
                mailMessage.SubjectEncoding = System.Text.Encoding.UTF8;

                try
                {
                    // Send the email with a proper try-catch block specifically for sending
                    smtpClient.Send(mailMessage);
                    _logger.LogInformation(
                        $"Password reset email sent to {existingMobileUser.Email} at {DateTime.UtcNow}");
                }
                catch (SmtpException smtpEx)
                {
                    _logger.LogError(smtpEx, $"SMTP error sending password reset email to {existingMobileUser.Email}");
                    // Still return success since the password was reset successfully
                }
                finally
                {
                    // Proper disposal of resources
                    mailMessage.Dispose();
                    smtpClient.Dispose();
                }

                return new RegistrationRes() { Result = true, Massage = "Reset Password Successful" };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ForgetPassword method");
                return new RegistrationRes()
                    { Result = false, Massage = "An error occurred while resetting password." };
            }
        }

        public async Task Logout()
        {
            await this._signInManager.SignOutAsync();
        }

        private static bool IsValidPhoneNumber(string phoneNumber)
        {
            //string pattern = @"^\+?[1-9]\d{1,14}$"; // E.164 format, for example

            // Pattern to match local or international phone numbers
            const string pattern = @"^(\+?[0-9]{1,4})?([-. \()]?\d{2,4}[-. \()]?)*\d{4,}$";
            return Regex.IsMatch(phoneNumber, pattern);
        }

        public static bool IsValidEmail(string email)
        {
            const string pattern = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";
            return Regex.IsMatch(email, pattern);
        }

        public async Task<RegistrationRes> Login(LoginRequest user)
        {
            try
            {
                const string errorMessage = "User Not Exist";

                const string passwordComp = "123456aA@";

                var isPhoneNumber = IsValidPhoneNumber(user.Email);
                var existingUser = await _userManager.Users
                    .FirstOrDefaultAsync(r =>
                        (isPhoneNumber ? r.PhoneNumber == user.Email : r.Email == user.Email) && !r.isDeleted);

                if (existingUser == null || existingUser.isDeleted)
                {
                    // We dont want to give to much information on why the request has failed for security reasons
                    _logger.LogWarning(errorMessage);
                    return new RegistrationRes()
                    {
                        Result = false,
                        Errors = [errorMessage]
                    };
                }

                //Can't moved this Befor Condation
                var rolename = await _userManager.GetRolesAsync(existingUser);
                existingUser.device_token = user.device_token;
                await _userManager.UpdateAsync(existingUser);
                var createCustomePass = user.Password + passwordComp;

                if (existingUser.Email is "<EMAIL>" or "<EMAIL>")
                {
                    createCustomePass = user.Password;
                }

                // Now we need to check if the user has inputed the right password
                var isCorrect = await _userManager.CheckPasswordAsync(existingUser, createCustomePass);
                if (isCorrect)
                {
                    var jwtToken = GenerateJwtToken(existingUser);
                    _logger.LogInformation("Login");
                    return new RegistrationRes()
                    {
                        Result = true,
                        Token = jwtToken,
                        Id = existingUser.Id,
                        RolesName = rolename.ToList(),
                        UserName = existingUser.UserName
                    };
                }
                else
                {
                    // We dont want to give to much information on why the request has failed for security reasons
                    _logger.LogWarning("the request has failed for security reasons");
                    return new RegistrationRes()
                    {
                        Result = false,
                        Errors = new List<string>()
                        {
                            "User Not Exist"
                        }
                    };
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }


        // private async Task<IdentityResult> addAdmin()
        // {
        //     ApplicationUser user;
        //     var existingUser = _userManager.Users.Where(r => r.PhoneNumber == "root" && r.isDeleted == false).FirstOrDefault();
        //     string AddEmail = "<EMAIL>";
        //     IdentityResult result = new IdentityResult();
        //     if (existingUser == null)
        //     {
        //         user = new ApplicationUser()
        //         {
        //             Email = AddEmail,
        //             UserName = AddEmail,
        //             EmailConfirmed = true,
        //             PhoneNumber = "root",
        //             isDeleted = false,
        //             Isapproved = true,
        //             CreatedBy = "root",
        //             Creatied_Date = DateTime.Now,
        //             LastEditeBy = "root",
        //             LastEiteDate = DateTime.Now,
        //         };
        //         Extention.DefaulltValuesForModel<ApplicationUser>(user);
        //
        //         result = await _userManager.CreateAsync(user, "123456789aA@");
        //
        //     }
        //     var existingUser2 =await _userManager.Users.Where(r => r.PhoneNumber == "123456789" && !r.isDeleted).FirstOrDefaultAsync();
        //     if (existingUser2 == null)
        //     {
        //         user = new ApplicationUser()
        //         {
        //             Email = "<EMAIL>",
        //             UserName = "<EMAIL>",
        //             EmailConfirmed = true,
        //             PhoneNumber = "123456789",
        //             isDeleted = false,
        //             Isapproved = true,
        //             CreatedBy = "Sys",
        //             Creatied_Date = DateTime.Now,
        //             LastEditeBy = "Sys",
        //             LastEiteDate = DateTime.Now,
        //             UserType = (int)AllEnumes.UserType.MiniEmployee,
        //
        //         };
        //         result = await _userManager.CreateAsync(user, "123456789aA@");
        //     }
        //     //employee
        //
        //     if (result.Succeeded)
        //     {
        //         _logger.LogInformation(AddEmail + " Is created ");
        //     }
        //     var resultAddAdminRole = await addAdminRole(AddEmail);
        //     return resultAddAdminRole;
        // }

/*
        private async Task<IdentityResult> AddAdminRole(string AddEmail)
        {
            string RoleName;
            string AddRoleName = "SuperAdmin";
            ApplicationUser UserModel = new ApplicationUser();
            IEnumerable<IdentityRole> ListRole = new List<IdentityRole>();
            ListRole = await _roleManager.Roles.ToListAsync();
            if (!(ListRole.Any(x => x.Name == AddRoleName)))
            {
                IdentityRole roles = new IdentityRole()
                {
                    Name = AddRoleName
                };
                var createRoleResult = await _roleManager.CreateAsync(roles);
                RoleName = roles.Name;
                _logger.LogInformation(AddRoleName + " Is created ");
            }
            else
            {
                RoleName = ListRole.Where(x => x.Name == AddRoleName).FirstOrDefault().Name;
            }
            if (await _userManager.FindByEmailAsync(AddEmail) != null)
            {
                UserModel = await _userManager.FindByEmailAsync(AddEmail);
            }
            else
            {
                UserModel = await _userManager.FindByEmailAsync(AddEmail);
            }
            return await _userManager.AddToRoleAsync(UserModel, RoleName);
        }
*/

        public async Task<ResultM<ApplicationUser>> CreateUser(DriverInfoDto user)
        {
            const string errorCreateUSer = "new Employee was not creaded";
            const string passwordComp = "123456aA@";
            var createCustomePass = user.Password + passwordComp;

            var existingUser = await _userManager.Users.Where(r => (r.UserName == user.Email && r.isDeleted == false))
                .FirstOrDefaultAsync();
            if (existingUser != null || user.Mobile == "123456789")
            {
                // We dont want to give to much information on why the request has failed for security reasons
                _logger.LogWarning("Username is  Exist");
                return new ResultM<ApplicationUser>()
                {
                    Object = null!,
                    Message = "Username is  Exist",
                    State = false
                };
            }

            try
            {
                var appUser = new ApplicationUser
                {
                    UserName = user.Email,
                    Email = user.Email,
                    PhoneNumber = user.Mobile,
                    UserType = 2,
                    lastNameEn = user.LastName,
                    lastNameAr = user.LastName,
                    FirstNameEn = user.FirstName,
                    FirstNameAr = user.FirstName,
                    isDeleted = false,
                    Image =
                        "http://149.102.138.16:98/api/Services/Display?name=user-Default-User.png&key=EmployeesImage"
                };

                var result = await _userManager.CreateAsync(appUser, createCustomePass);
                const string errorMessage = "new Employee was created";
                if (!result.Succeeded)
                    return new ResultM<ApplicationUser>()
                    {
                        Object = appUser,
                        Message = errorCreateUSer,
                        State = false
                    };
                _logger.LogInformation(errorMessage);
                return new ResultM<ApplicationUser>()
                {
                    Object = appUser,
                    Message = errorMessage,
                    State = true
                };
            }

            catch (Exception ex)
            {
                var result = new ResultM<ApplicationUser>()
                {
                    Object = null!,
                    Message = errorCreateUSer,
                    State = false
                };
                _logger.LogError(ex.Message);
                return result;
            }
        }

        public async Task<ResultM<UserDetailsDto?>> Details(string id)
        {
            ResultM<UserDetailsDto?>? result;
            try
            {
                var user = await _userManager.FindByIdAsync(id);

                if (user != null)
                {
                    switch (user.UserType)
                    {
                        case 2:
                        {
                            var model = ExpressionMapperUtility.Map<ApplicationUser, UserDetailsDto>(user);
                            model!.UserDetailsInfo =
                                _userDetailsInfoRepository.GetAllQuery().FirstOrDefault(x => x.UserId == user.Id);
                            model!.DriverLicense =
                                _driverLicenseRepository.GetAllQuery().FirstOrDefault(x => x.UserId == user.Id);
                            model!.CarInfo = _carInfoRepository.GetAllQuery().FirstOrDefault(x => x.UserId == user.Id);
                            model!.Mobile = user.PhoneNumber;
                            const string message = "Get Details of an Employee";
                            _logger.LogInformation(message + id);
                            result = new ResultM<UserDetailsDto?>()
                            {
                                Message = message + id,
                                State = true,
                                Object = model
                            };
                            return result;
                        }
                        case 1:
                        {
                            var model = ExpressionMapperUtility.Map<ApplicationUser, UserDetailsDto>(user);
                            model!.UserDetailsInfo =
                                _userDetailsInfoRepository.GetAllQuery().FirstOrDefault(x => x.UserId == user.Id);
                            model!.Mobile = user.PhoneNumber;
                            const string message = "Get Details of an Employee";
                            _logger.LogInformation(message + id);
                            result = new ResultM<UserDetailsDto?>()
                            {
                                Message = message + id,
                                State = true,
                                Object = model
                            };
                            return result;
                        }
                    }
                }
                else
                {
                    const string errorMessage = "Model is not valid";
                    _logger.LogInformation(errorMessage);
                    result = new ResultM<UserDetailsDto?>()
                    {
                        Message = errorMessage + id,
                        State = false,
                        Object = null
                    };
                    return result;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
                result = new ResultM<UserDetailsDto?>()
                {
                    Message = ex.Message,
                    State = false,
                    Object = null
                };
                return result;
            }

            return null!;
        }

        public async Task<ResultM<UserDto?>> GetClientDetails(string id)
        {
            ResultM<UserDto> result;
            try
            {
                var user = await _userManager.FindByIdAsync(id);

                if (user != null)
                {
                    var model = new UserDto()
                    {
                        FirstName = user.FirstNameEn,
                        LastName = user.lastNameEn,
                        Mobile = user.PhoneNumber!,
                        Email = user.Email,
                        UserType = user.UserType,
                        ClientType = user.ClientType,
                        Image = user.Image,
                        ClientInfo =
                            _companyInfoRepository.GetAllQuery()
                                .Select(x => new ClientCompanyInfoDto
                                {
                                    Name = x.Name,
                                    Address = x.Address,
                                    Description = x.Description,
                                    CommercialRegisterImg = x.CommercialRegisterImg
                                })
                                .FirstOrDefault()
                    };
                    const string message = "Get Details of an Employee";
                    _logger.LogInformation(message + id);
                    result = new ResultM<UserDto>()
                    {
                        Message = message + id,
                        State = true,
                        Object = model
                    };
                }
                else
                {
                    const string errorMessage = "Model is not valid";
                    _logger.LogInformation(errorMessage);
                    result = new ResultM<UserDto>()
                    {
                        Message = errorMessage + id,
                        State = false,
                        Object = null
                    };
                }

                return result!;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
                result = new ResultM<UserDto?>()
                {
                    Message = ex.Message,
                    State = false,
                    Object = null
                }!;
                return result;
            }
        }

        public async Task<ResultM<Dictionary<string, dynamic>>> Update(EditDriverInfo model)
        {
            var user = await _userManager.FindByIdAsync(model.Id);
            var userExist = await _userManager.Users.Where(res => res.UserName == model.Email && !res.isDeleted)
                .FirstOrDefaultAsync();
            var res = new Dictionary<string, dynamic>();
            ResultM<Dictionary<string, dynamic>> resultM;
            if (userExist != null && user!.UserName != model.Email)
            {
                const string errorMessage = "This UserName cannot be updated because it exists";
                res.Add("Errors", errorMessage);
                _logger.LogError(errorMessage);
                resultM = new ResultM<Dictionary<string, dynamic>>()
                {
                    Message = errorMessage,
                    State = false,
                    Object = res
                };
                return resultM;
            }
            else
            {
                try
                {
                    if (user != null)
                    {
                        user.Email = model.Email == "" ? user.Email : model.Email;
                        user.lastNameEn = model.LastName == "" ? user.lastNameEn : model.LastName;
                        user.FirstNameEn = model.FirstName == "" ? user.FirstNameEn : model.FirstName;
                        user.PhoneNumber = model.Mobile == "" ? user.PhoneNumber : model.Mobile;
                        if (!string.IsNullOrEmpty(user.Email))
                        {
                            user.Email = model.Email;
                        }

                        else
                        {
                            const string errorMessage = "Email cannot be empty";
                            _logger.LogError(errorMessage);
                            res.Add("Errors", errorMessage);
                            resultM = new ResultM<Dictionary<string, dynamic>>()
                            {
                                Message = errorMessage,
                                State = false,
                                Object = res
                            };
                            return resultM;
                        }

                        if (!string.IsNullOrEmpty(model.Email))
                        {
                            var result = await _userManager.UpdateAsync(user);
                            const string message = "Successfully Updated";
                            if (result.Succeeded)
                            {
                                res.Add("Message", message);
                                _logger.LogInformation(message);
                                resultM = new ResultM<Dictionary<string, dynamic>>
                                {
                                    Message = message,
                                    State = true,
                                    Object = res
                                };
                            }
                            else
                            {
                                res.Add("Error", message);
                                var errors = result.Errors.ToArray();
                                var errorMessage = string.Join("; ", errors.Select(e => e.ToString()));
                                _logger.LogError("Multiple errors occurred: {Errors}", errorMessage);

                                resultM = new ResultM<Dictionary<string, dynamic>>
                                {
                                    Message = errorMessage, // Using the joined error messages instead of ToString()
                                    State = false,
                                    Object = res
                                };
                            }
                        }
                        else
                        {
                            const string errorMessage = "Error in Updating";
                            res.Add("Error", errorMessage);
                            _logger.LogError(errorMessage);
                            resultM = new ResultM<Dictionary<string, dynamic>>
                            {
                                Message = errorMessage,
                                State = false,
                                Object = res
                            };
                        }
                    }
                    else
                    {
                        const string errorMessage = "User Not Found";
                        _logger.LogError(errorMessage);
                        resultM = new ResultM<Dictionary<string, dynamic>>()
                        {
                            Message = errorMessage,
                            State = false,
                            Object = null!
                        };
                    }

                    return resultM;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex.Message);
                    resultM = new ResultM<Dictionary<string, dynamic>>()
                    {
                        Message = ex.Message,
                        State = false,
                        Object = null!
                    };
                    return resultM;
                }
            }
        }

        public async Task<ResultM<Dictionary<string, dynamic>>> DeleteUser(string id)
        {
            var user = await _userManager.FindByIdAsync(id);
            ResultM<Dictionary<string, dynamic>> resultM;
            var res = new Dictionary<string, dynamic>();
            if (user != null)
            {
                if (user.isDeleted)
                {
                    _logger.LogInformation("Deleted");
                    res.Add("Message", "User has already been deleted");
                    resultM = new ResultM<Dictionary<string, dynamic>>()
                    {
                        Message = "User has already been deleted",
                        State = false,
                        Object = res
                    };
                    return resultM;
                }

                user.isDeleted = true;
                var result = await _userManager.UpdateAsync(user);
                if (result.Succeeded)
                {
                    _logger.LogInformation("Deleted");
                    res.Add("Message", "Deleted");
                    resultM = new ResultM<Dictionary<string, dynamic>>()
                    {
                        Message = "Deleted",
                        State = true,
                        Object = res
                    };
                }
                else
                {
                    const string error = "an error occurred";
                    _logger.LogError(error);
                    res.Add("Error", "Deleted");
                    resultM = new ResultM<Dictionary<string, dynamic>>()
                    {
                        Message = "an error occurred",
                        State = false,
                        Object = res
                    };
                }
            }
            else
            {
                const string error = "User Not Found";
                _logger.LogError(error);
                res.Add("Error", error);
                resultM = new ResultM<Dictionary<string, dynamic>>()
                {
                    Message = "an error occurred",
                    State = false,
                    Object = res
                };
            }

            return resultM;
        }

        public async Task<ResultM<Dictionary<string, dynamic>>> GetUsersByName(string name)
        {
            var res = new Dictionary<string, dynamic>();
            var list = await _userManager.Users.Where(r => !r.isDeleted).ToListAsync();
            var userslist =
                (from item in list
                    let UserName = item.UserName.Contains(name, StringComparison.CurrentCultureIgnoreCase)
                    where UserName
                    select item).ToList();
            res.Add("usersData", userslist);
            var obj = new ResultM<Dictionary<string, dynamic>>()
            {
                Object = res,
                Message = "Done",
                State = true
            };
            return obj;
        }


        public ResultM<UsersDto> Users()
        {
            ResultM<UsersDto> result;

            //return _UserRepository.GetAllQuery().ToListAsync();
            var users = _userManager.Users.ToList();

            if (users != null)
            {
                var usersDtoList = users.Select(ExpressionMapperUtility.Map<ApplicationUser, UsersDto>).ToList();

                const string message = "Get All Users ";
                _logger.LogInformation(message);
                result = new ResultM<UsersDto>()
                {
                    Message = message,
                    State = true,
                    Objects = usersDtoList!
                };
            }
            else
            {
                const string errorMessage = "No Data";
                _logger.LogInformation(errorMessage);
                result = new ResultM<UsersDto>()
                {
                    Message = errorMessage,
                    State = false
                };
            }

            return result;
        }

        public IEnumerable<ApplicationUser> GetAll()
        {
            throw new NotImplementedException();
        }

        public ApplicationUser GetById(int id)
        {
            throw new NotImplementedException();
        }

        public void Add(ApplicationUser entity)
        {
            throw new NotImplementedException();
        }

        public void Update(ApplicationUser entity)
        {
            throw new NotImplementedException();
        }

        public void Delete(ApplicationUser entity)
        {
            throw new NotImplementedException();
        }

        public void Delete(int id)
        {
            throw new NotImplementedException();
        }

        public IQueryable<ApplicationUser> GetAllQuery(Guid userId)
        {
            throw new NotImplementedException();
        }

        public async Task<Dictionary<string, dynamic>> UploadImge(IFormFile file, string userid, string type)
        {
            var obj = new Dictionary<string, dynamic>();
            try
            {
                if (file == null)
                {
                    obj = new Dictionary<string, dynamic>()
                    {
                        { "Message", "Image not selected" },
                        { "MessageAR", "لم يتم تحديد الصوره" }
                    };
                    return obj;
                }

                var fileExtension = Path.GetExtension(file.FileName).ToLower();
                var folderName = "uploads/" + userid + "files";
                var pathToSave = Path.Combine(folderName);
                if (!Directory.Exists(pathToSave))
                {
                    Directory.CreateDirectory(pathToSave);
                }

                var imageName = Regex.Replace(Path.GetFileNameWithoutExtension(file.FileName), @"\s", "");
                var fileName = imageName + DateTime.Now.Ticks.ToString() + Path.GetExtension(file.FileName);

                var fullPath = Path.Combine(pathToSave, fileName);
                await using (var stream = new FileStream(fullPath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                if (file.Length > 150000)
                {
                    ResizeAndSaveImage(fullPath);
                }

                var user = await _userManager.Users.Where(r => r.Id == userid).FirstOrDefaultAsync();
                var driverLicense = await _driverLicenseRepository.GetAllQuery().Where(r => r.UserId == userid)
                    .FirstOrDefaultAsync();
                var carInfo = await _carInfoRepository.GetAllQuery().Where(r => r.UserId == userid)
                    .FirstOrDefaultAsync();
                var companyInfo = await _companyInfoRepository.GetAllQuery().Where(r => r.ClientId == userid)
                    .FirstOrDefaultAsync();
                var userDetails = await _userDetailsInfoRepository.GetAllQuery().Where(r => r.UserId == userid)
                    .FirstOrDefaultAsync();
                user!.path = pathToSave;
                await _userManager.UpdateAsync(user);
                var url = _configuration["Urllive"] + "/api/Accounts/" + userid + "/Display?name=" + fileName;
                switch (type.Trim())
                {
                    case "UImage":
                        user.Image = url;
                        await _userManager.UpdateAsync(user);
                        break;
                    case "DriverImage":
                        user.Image = url;
                        break;
                    case "FrontLicenseImage":
                        driverLicense!.FrontLicenseImage = url;
                        _driverLicenseRepository.Update(driverLicense);
                        break;
                    case "BackLicenseImage":
                        driverLicense!.BackLicenseImage = url;
                        _driverLicenseRepository.Update(driverLicense);
                        break;
                    case "PersonalLicenseImg":
                        driverLicense!.PersonalLicenseImg = url;
                        _driverLicenseRepository.Update(driverLicense);
                        break;
                    case "certImage":
                        driverLicense!.DrCertGConductImage = url;
                        _driverLicenseRepository.Update(driverLicense);
                        break;
                    case "frontNationalIDImg":
                        userDetails!.FrontNationalIDImg = url;
                        _userDetailsInfoRepository.Update(userDetails);
                        break;
                    case "bBackNationalIDImg":
                        userDetails!.BBackNationalIDImg = url;
                        _userDetailsInfoRepository.Update(userDetails);
                        break;
                    case "crcarRegistrationImgF":
                        carInfo!.CrcarRegistrationImgF = url;
                        _carInfoRepository.Update(carInfo);
                        break;
                    case "CrcarRegistrationImgB":
                        carInfo!.CrcarRegistrationImgB = url;
                        _carInfoRepository.Update(carInfo);
                        break;
                    case "CarImg":
                        carInfo!.CarImg = url;
                        _carInfoRepository.Update(carInfo);
                        break;
                    case "ClCommercialRegister":
                        companyInfo!.CommercialRegisterImg = url;
                        _companyInfoRepository.Update(companyInfo);
                        break;
                }

                const string message = "Image Saved !";
                const string messageAr = "تم حفظ الصوره !";
                obj.Add("Message", message);
                obj.Add("MessageAR", messageAr);
                obj.Add("Status", true);

                return obj;
            }
            catch (Exception ex)
            {
                const string message = "Image Not Saved !";
                const string messageAr = "لم يتم حفظ الصوره !";
                obj.Add("Status", false);

                obj.Add("Message", message);
                obj.Add("MessageAR", messageAr);
                obj.Add("MessageEx", ex.Message);
                return obj;
            }
        }

        private static void ResizeAndSaveImage(string filename)
        {
            using var image = Image.Load<Rgba32>(filename);
            image.Mutate(x => x
                .Resize(image.Width / 4, image.Height / 4)
            );
            image.Save(filename); // Automatic encoder selected based on extension.
        }

        public Task<ResultFileDTO> Display(string name, string userid)
        {
            ResultFileDTO obj;
            if (name == null)
            {
                obj = new ResultFileDTO()
                {
                    contentType = null!,
                    fileContents = null!,
                    Message = "name not present",
                    MessageAR = "برجاء ادخال الاسم",
                    State = false
                };
                return Task.FromResult(obj);
            }

            var fileExtension = Path.GetExtension(name);
            var path = Path.Combine(@"uploads/" + userid + "files", name);

            var filepath = Path.Combine(path);
            var imageArry = System.IO.File.ReadAllBytes(filepath.Replace("//", "\\"));
            var base64ImageRepresantation = Convert.ToBase64String(imageArry);
            var picture = Convert.FromBase64String(base64ImageRepresantation);
            dynamic type = "image/" + fileExtension.Split('.')[1];
            const string message = "name present";
            const string messageAr = "تم ايجاد الاسم";
            obj = new ResultFileDTO()
            {
                contentType = type,
                fileContents = picture,
                Message = message,
                MessageAR = messageAr,
                State = true
            };
            return Task.FromResult(obj);
        }

        public async Task<ResultM<UserCompletionStatusDto>> CheckUserCompletionStatus(string userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null || user.isDeleted)
                {
                    return new ResultM<UserCompletionStatusDto>()
                    {
                        Message = "User not found",
                        State = false,
                        Object = null
                    };
                }

                var completionStatus = new UserCompletionStatusDto();
                var missingFields = new List<string>();
                var missingDocuments = new List<string>();
                var completionDetails = new UserCompletionDetails();

                // Check basic user information
                completionDetails.BasicInfoComplete = CheckBasicUserInfo(user, missingFields);

                switch (user.UserType)
                {
                    case 2: // Driver
                        await CheckDriverCompletion(userId, user, completionDetails, missingFields, missingDocuments);
                        break;
                    case 3: // Client
                        await CheckClientCompletion(userId, user, completionDetails, missingFields, missingDocuments);
                        break;
                    default:
                        completionDetails.BasicInfoComplete = true;
                        break;
                }

                // Calculate completion percentage and overall status
                var totalChecks = GetTotalChecksForUserType(user.UserType);
                var completedChecks = GetCompletedChecksCount(completionDetails, user.UserType);

                completionStatus.CompletionPercentage = totalChecks > 0 ? (completedChecks * 100) / totalChecks : 100;
                completionStatus.IsComplete = missingFields.Count == 0 && missingDocuments.Count == 0;
                completionStatus.MissingFields = missingFields;
                completionStatus.MissingDocuments = missingDocuments;
                completionStatus.CompletionDetails = completionDetails;

                return new ResultM<UserCompletionStatusDto>()
                {
                    Message = "User completion status retrieved successfully",
                    State = true,
                    Object = completionStatus
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking user completion status for user {UserId}", userId);
                return new ResultM<UserCompletionStatusDto>()
                {
                    Message = "Error checking user completion status",
                    State = false,
                    Object = null
                };
            }
        }

        private bool CheckBasicUserInfo(ApplicationUser user, List<string> missingFields)
        {
            var isComplete = true;

            if (string.IsNullOrWhiteSpace(user.FirstNameEn))
            {
                missingFields.Add("First Name");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(user.lastNameEn))
            {
                missingFields.Add("Last Name");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(user.PhoneNumber))
            {
                missingFields.Add("Phone Number");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(user.Email))
            {
                missingFields.Add("Email");
                isComplete = false;
            }

            return isComplete;
        }

        private async Task CheckDriverCompletion(string userId, ApplicationUser user, UserCompletionDetails completionDetails,
            List<string> missingFields, List<string> missingDocuments)
        {
            // Check UserDetailsInfo (Personal Details)
            var userDetails = await _userDetailsInfoRepository.GetAllQuery()
                .FirstOrDefaultAsync(x => x.UserId == userId);

            completionDetails.PersonalDetailsComplete = CheckUserDetailsInfo(userDetails, missingFields, missingDocuments);

            // Check DriverLicense
            var driverLicense = await _driverLicenseRepository.GetAllQuery()
                .FirstOrDefaultAsync(x => x.UserId == userId);

            completionDetails.DriverLicenseComplete = CheckDriverLicense(driverLicense, missingFields, missingDocuments);

            // Check CarInfo
            var carInfo = await _carInfoRepository.GetAllQuery()
                .FirstOrDefaultAsync(x => x.UserId == userId);

            completionDetails.CarInfoComplete = CheckCarInfo(carInfo, missingFields, missingDocuments);

            // Check if profile image is uploaded
            if (string.IsNullOrWhiteSpace(user.Image) || user.Image.Contains("user-Default-User.png"))
            {
                missingDocuments.Add("Profile Image");
            }

            completionDetails.DocumentsComplete = missingDocuments.Count == 0;
        }

        private async Task CheckClientCompletion(string userId, ApplicationUser user, UserCompletionDetails completionDetails,
            List<string> missingFields, List<string> missingDocuments)
        {
            // For company clients, check company information
            if (user.ClientType == 2)
            {
                var companyInfo = await _companyInfoRepository.GetAllQuery()
                    .FirstOrDefaultAsync(x => x.ClientId == userId);

                completionDetails.CompanyInfoComplete = CheckCompanyInfo(companyInfo, missingFields, missingDocuments);
            }
            else
            {
                completionDetails.CompanyInfoComplete = true; // Not required for personal clients
            }

            // Check if profile image is uploaded
            if (string.IsNullOrWhiteSpace(user.Image) || user.Image.Contains("user-Default-User.png"))
            {
                missingDocuments.Add("Profile Image");
            }

            completionDetails.DocumentsComplete = missingDocuments.Count == 0;
        }

        private bool CheckUserDetailsInfo(UserDetailsInfo? userDetails, List<string> missingFields, List<string> missingDocuments)
        {
            if (userDetails == null)
            {
                missingFields.Add("Personal Details");
                return false;
            }

            var isComplete = true;

            if (userDetails.BirthDate == default(DateTime))
            {
                missingFields.Add("Birth Date");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(userDetails.NationalID))
            {
                missingFields.Add("National ID");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(userDetails.Governorate))
            {
                missingFields.Add("Governorate");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(userDetails.District))
            {
                missingFields.Add("District");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(userDetails.Street))
            {
                missingFields.Add("Street");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(userDetails.BuildingNo))
            {
                missingFields.Add("Building Number");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(userDetails.FrontNationalIDImg))
            {
                missingDocuments.Add("Front National ID Image");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(userDetails.BBackNationalIDImg))
            {
                missingDocuments.Add("Back National ID Image");
                isComplete = false;
            }

            return isComplete;
        }

        private bool CheckDriverLicense(DriverLicense? driverLicense, List<string> missingFields, List<string> missingDocuments)
        {
            if (driverLicense == null)
            {
                missingFields.Add("Driver License Information");
                return false;
            }

            var isComplete = true;

            if (string.IsNullOrWhiteSpace(driverLicense.LicenseNo))
            {
                missingFields.Add("License Number");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(driverLicense.DateOfExpire))
            {
                missingFields.Add("License Expiry Date");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(driverLicense.FrontLicenseImage))
            {
                missingDocuments.Add("Front License Image");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(driverLicense.BackLicenseImage))
            {
                missingDocuments.Add("Back License Image");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(driverLicense.PersonalLicenseImg))
            {
                missingDocuments.Add("Personal License Image");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(driverLicense.DrCertGConductImage))
            {
                missingDocuments.Add("Certificate of Good Conduct");
                isComplete = false;
            }

            return isComplete;
        }

        private bool CheckCarInfo(CarInfo? carInfo, List<string> missingFields, List<string> missingDocuments)
        {
            if (carInfo == null)
            {
                missingFields.Add("Car Information");
                return false;
            }

            var isComplete = true;

            if (string.IsNullOrWhiteSpace(carInfo.Transport))
            {
                missingFields.Add("Transport Type");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(carInfo.NumberPlate))
            {
                missingFields.Add("Number Plate");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(carInfo.CarImg))
            {
                missingDocuments.Add("Car Image");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(carInfo.CrcarRegistrationImgF))
            {
                missingDocuments.Add("Front Car Registration Image");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(carInfo.CrcarRegistrationImgB))
            {
                missingDocuments.Add("Back Car Registration Image");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(carInfo.CarProductionYear))
            {
                missingFields.Add("Car Production Year");
                isComplete = false;
            }

            return isComplete;
        }

        private bool CheckCompanyInfo(Company? companyInfo, List<string> missingFields, List<string> missingDocuments)
        {
            if (companyInfo == null)
            {
                missingFields.Add("Company Information");
                return false;
            }

            var isComplete = true;

            if (string.IsNullOrWhiteSpace(companyInfo.Name))
            {
                missingFields.Add("Company Name");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(companyInfo.Address))
            {
                missingFields.Add("Company Address");
                isComplete = false;
            }

            if (string.IsNullOrWhiteSpace(companyInfo.CommercialRegisterImg))
            {
                missingDocuments.Add("Commercial Register Image");
                isComplete = false;
            }

            return isComplete;
        }

        private int GetTotalChecksForUserType(int userType)
        {
            return userType switch
            {
                2 => 6, // Driver: BasicInfo, PersonalDetails, DriverLicense, CarInfo, Documents, (CompanyInfo not applicable)
                3 => 3, // Client: BasicInfo, Documents, CompanyInfo (if company type)
                _ => 1  // Other: BasicInfo only
            };
        }

        private int GetCompletedChecksCount(UserCompletionDetails completionDetails, int userType)
        {
            var count = 0;

            if (completionDetails.BasicInfoComplete) count++;

            switch (userType)
            {
                case 2: // Driver
                    if (completionDetails.PersonalDetailsComplete) count++;
                    if (completionDetails.DriverLicenseComplete) count++;
                    if (completionDetails.CarInfoComplete) count++;
                    if (completionDetails.DocumentsComplete) count++;
                    break;
                case 3: // Client
                    if (completionDetails.DocumentsComplete) count++;
                    if (completionDetails.CompanyInfoComplete) count++;
                    break;
            }

            return count;
        }
    }
}