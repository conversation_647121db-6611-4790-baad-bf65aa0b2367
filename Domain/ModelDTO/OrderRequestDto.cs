namespace Domain.ModelDTO;

public class OrderRequestDto
{
    public required string ClientId { get; set; }
    public required string FromName { get; set; }
    public required string FromLocation { get; set; }
    public required string ToName { get; set; }
    public required string ToLocation { get; set; }
    public required int TransportId { get; set; }
    public string? Distination { get; set; }
    public double? Price { get; set; }
    public string? Description { get; set; }
}