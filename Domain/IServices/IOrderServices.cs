using System.Data;
using Domain.Entities;
using Domain.EnumsModel;
using Domain.ModelDTO;

namespace Domain.IServices;

public interface IOrderServices:IService<Order>
{
    IEnumerable<OrderDto> GetOrders();
    public OrderDto GetOrder(int id);
    double? GetTotal(double distance);
    bool ApproveOrder(int orderId, string driverId);
    IEnumerable<OrderApprovalDto> GetApproveOrders();
    IEnumerable<OrderApprovalDto> GetApproveOrdersByDriverId(string driverId, string state);
    IEnumerable<OrderDto> GetOrdersByClientId(string clientId);
   Order UpdateStatus (int id, OrderStatus status);
}