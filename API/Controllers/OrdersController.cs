using AutoMapper;
using Domain.Entities;
using Domain.EnumsModel;
using Domain.IServices;
using Domain.ModelDTO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public class OrdersController : ControllerBase
{
    private readonly IOrderServices _orderServices;
    private readonly IMapper _mapper;
    private readonly Dictionary<string, dynamic?> _resDictionary = new();

    public OrdersController(IOrderServices orderServices, IMapper mapper)
    {
        _orderServices = orderServices;
        _mapper = mapper;
    }

    [HttpGet("GetOrders")]
    public ActionResult GetOrders()
    {
        return Ok(_orderServices.GetOrders());
    }
    
    [HttpGet("GetOrderById/{id}")]
    public ActionResult GetOrderById(int id)
    {
        return Ok(_orderServices.GetOrder(id));
    }
    [HttpGet("GetOrdersByClientId/{clientId}")]
    public ActionResult GetOrdersByClientId(string clientId)
    {
        return Ok(_orderServices.GetOrdersByClientId(clientId));
    }
    [HttpPost("Order")]
    public ActionResult Settings([FromBody] OrderRequestDto entity)
    {
        var order = _mapper.Map<Order>(entity);
        _orderServices.Add(order);
        return Ok(entity);
    }

    [HttpGet("CalculateDistance")]
    public ActionResult CalculateDistance(DistanceDto entity)
    {
        _resDictionary.Add("Price", _orderServices.GetTotal(entity.Distance));
        return Ok(_resDictionary);
    }

    // New Endpoint for Order Approval
    [HttpPost("ApproveOrder")]
    public ActionResult ApproveOrder([FromBody] OrderApprovalRequestDto request)
    {
        var result = _orderServices.ApproveOrder(request.OrderId, request.DriverId);
        if (!result)
        {
            _resDictionary.Add("Message", "Order approval failed or order not found.");
            return BadRequest(_resDictionary);
        }

        _resDictionary.Add("Message", "Order approved successfully.");
        return Ok(_resDictionary);
    }
    
    [HttpGet("GetApproveOrders")]
    public ActionResult GetApproveOrders()
    {
        return Ok(_orderServices.GetApproveOrders());
    }
    
    [HttpGet("GetApproveOrdersByDriverId")]
    public ActionResult GetApproveOrdersByDriverId(string driverId,string state)
    {
        return Ok(_orderServices.GetApproveOrdersByDriverId(driverId,state));
    }

    [HttpPut("UpdateOrderStatus/{id}/{status}")]
    public ActionResult UpdateOrderStatus(int id, OrderStatus status)
    {
        var updatedOrder = _orderServices.UpdateStatus(id, status);

        if (updatedOrder == null)
            return NotFound("Order not found");
        
        return Ok(updatedOrder);
    }
    
    [HttpGet("OrderStatus")]
    public IActionResult GetOrderStatuses()
    {
        var statuses = Enum.GetNames(typeof(OrderStatus));
        return Ok(statuses);
    }
}