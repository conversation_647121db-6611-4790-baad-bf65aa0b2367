using System.Security.Claims;
using Domain.Entities;
using Domain.IServices;
using Domain.ModelDTO;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using AutoMapper;
using Domain.BasicModels;

namespace API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AccountsController : ControllerBase
    {
        private readonly ILogger<AccountsController> _logger;
        private readonly IUserServices _accounttService;
        private readonly IOtpServices _otpServices;
        private readonly Dictionary<string, string> _resDictionary = new();
        public readonly IMapper Mapper;

        public AccountsController(ILogger<AccountsController> logger, IMapper mapper, IUserServices accounttService,
            IOtpServices otpServices)
        {
            _accounttService = accounttService;
            _logger = logger;
            _otpServices = otpServices;
            Mapper = mapper;
        }

        [HttpPost, AllowAnonymous]
        [Route("RegisterUser")]
        public async Task<IActionResult> Register([FromBody] UserDto user)
        {
            _logger.LogError("Inputs Register", user);

            if (ModelState.IsValid)
            {
                user.UserType = 3;
                var result = await _accounttService.RegisterClient(user);
                if (result.Result)
                {
                    return Ok(result);
                }
                else
                {
                    var reternDto = new JsonResult(new RegistrationRes()
                        {
                            Result = false,
                            Errors = result.Errors!.ToList()
                        })
                        { StatusCode = 500 };

                    return BadRequest(reternDto);
                }
            }
            else
            {
                _logger.LogError("Model State IsValid when Register");
                return BadRequest(new RegistrationRes()
                {
                    Result = false,
                    Errors = ["Invalid payload"]
                });
            }
        }

        [HttpPost, AllowAnonymous]
        [Route("SignUp")]
        public async Task<IActionResult> RegisterDriver([FromBody] DriverDto user)
        {
            _logger.LogError("Inputs Register", user);

            if (ModelState.IsValid)
            {
                var result = await _accounttService.Register(user);
                if (result.Result)
                {
                    return Ok(result);
                }
                else
                {
                    var reternDto = new JsonResult(new RegistrationRes()
                        {
                            Result = false,
                            Errors = result.Errors!.ToList()
                        })
                        { StatusCode = 500 };

                    return BadRequest(reternDto);
                }
            }
            else
            {
                _logger.LogError("Model State IsValid when Register");
                return BadRequest(new RegistrationRes()
                {
                    Result = false,
                    Errors = ["Invalid payload"]
                });
            }
        }
        [HttpPost, AllowAnonymous]
        [Route("ForgetPassword")]
        public async Task<IActionResult> ForgetPassword([FromBody] ForgetPassword user)
        {
            _logger.LogError("inputs forget password", user);

            if (ModelState.IsValid)
            {
                var result = await _accounttService.ForgetPassword(user);
                if (result.Result)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            else
            {
                _logger.LogError("Model State IsValid when ResetPassword");
                return BadRequest("Model State IsValid when ResetPassword");
            }
        }

        [HttpPost("ResetPassword"), AllowAnonymous]
        public async Task<IActionResult> ResetPassword([FromBody] ResetPassword user)
        {
            _logger.LogError("inputs forget password", user);

            if (ModelState.IsValid)
            {
                var result = await _accounttService.ResetPassword(user);
                if (result.Result)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            else
            {
                _logger.LogError("Model State IsValid when ResetPassword");
                return BadRequest("Model State IsValid when ResetPassword");
            }
        }

        [HttpPost, AllowAnonymous]
        [Route("Login")]
        public async Task<IActionResult> Login([FromBody] LoginRequest user)
        {
            _logger.LogError("Inputs Login", user);

            if (ModelState.IsValid)
            {
                var result = await _accounttService.Login(user);
                if (result.Result)
                {
                    return Ok(result);
                }
                else
                {
                    var ret = new JsonResult(new RegistrationRes()
                        {
                            Result = false,
                            Errors = result.Errors!.ToList()
                        })
                        { StatusCode = 400 };

                    return BadRequest(ret);
                }
            }

            _logger.LogError("Invalid payload");
            return BadRequest(new RegistrationRes()
            {
                Result = false,
                Errors = ["Invalid payload"]
            });
        }

        [HttpGet(nameof(Logout))]
        public async Task<ActionResult> Logout()
        {
            await _accounttService.Logout();
            return Ok();
        }

        [HttpGet("GetAllUsers")]
        public ActionResult GetAllUsers()
        {
            var listUser = _accounttService.Users();
            if (listUser.Objects.Count != 0)
            {
                return Ok(listUser);
            }
            else
            {
                _logger.LogError("GetAllUsers Errors", listUser);

                return Content("List Of  Users is Empety");
            }
        }

        [HttpPost("Create")]
        public async Task<IActionResult> CreateUser(DriverInfoDto user)
        {
            var createUser = await _accounttService.CreateUser(user);
            if (createUser.State)
            {
                return Ok(createUser);
            }
            else
            {
                _logger.LogError("CreateUser Errors", createUser);

                return Content(createUser.Message!);
            }
        }

        [HttpGet("Details")]
        [Authorize] // Make sure the endpoint requires authentication
        public async Task<IActionResult> Details()
        {
            // Get the user ID from the token claims
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(userId))
            {
                _logger.LogError("User ID not found in token");
                return Unauthorized("User ID not found in token");
            }

            var details = await _accounttService.Details(userId);
            if (details.State)
            {
                return Ok(details.Object);
            }
            else
            {
                _logger.LogError("UserDetails Errors", details);
                return Content(details.Message!);
            }
        }
        [HttpGet("ClientDetails")]
        [Authorize] // Make sure the endpoint requires authentication
        public async Task<IActionResult> ClientDetails()
        {
            // Get the user ID from the token claims
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(userId))
            {
                _logger.LogError("User ID not found in token");
                return Unauthorized("User ID not found in token");
            }

            var details = await _accounttService.GetClientDetails(userId);
            if (details.State)
            {
                return Ok(details.Object);
            }
            else
            {
                _logger.LogError("UserDetails Errors", details);
                return Content(details.Message!);
            }
        }
        [HttpPost("Update")]
        public async Task<IActionResult> Update(EditDriverInfo model)
        {
            _logger.LogError("Inputs Update", model);

            var details = await _accounttService.Update(model);
            if (details.State)
            {
                return Ok(details.Object);
            }
            else
            {
                _logger.LogError("Errors Update", details);

                return BadRequest(details.Object);
            }
        }

        [HttpPost("Delete")]
        public async Task<IActionResult> DeleteUser(string id)
        {
            _logger.LogError("Inputs DeleteUser", id);

            var details = await _accounttService.DeleteUser(id);
            if (details.State)
            {
                return Ok(details.Object);
            }
            else
            {
                _logger.LogError("Errors DeleteUser", details);

                return BadRequest(details.Object);
            }
        }


        [HttpGet("GetUsersByName")]
        public async Task<IActionResult> GetUsersByName(string name)
        {
            _logger.LogError("Inputs GetUsersByName", name);

            var res = await _accounttService.GetUsersByName(name);
            return Ok(res);
        }

        [HttpPost, AllowAnonymous]
        [Route("SendOTP")]
        public Task<ActionResult> SendOtp(OtpDto otp)
        {
            var model = new Otp { PhoneEmail = otp.PhoneEmail, Firebase = otp.Firebase };
            _otpServices.Add(model);
            _resDictionary.Add("Message", "Otp Send Successfully");
            return Task.FromResult<ActionResult>(Ok(_resDictionary));
        }

        [HttpPost, AllowAnonymous]
        [Route("SendEmailOTP")]
        public Task<ActionResult> SendEmailOtp(OtpDto otp)
        {
            _otpServices.SendOtpByEmail(otp.PhoneEmail);
            _resDictionary.Add("Message", "Otp Send Successfully");
            return Task.FromResult<ActionResult>(Ok(_resDictionary));
        }

        [HttpPost, AllowAnonymous]
        [Route("VerificationOTP")]
        public Task<ActionResult> VerificationOtp(VOtpDto otp)
        {
            try
            {
                var model = new Otp { PhoneEmail = otp.PhoneEmail, OtpNo = otp.OtpNo };
                var res = _otpServices.VerificationOtp(model);
                _resDictionary.Add("Message", res.MessageEn);
                _resDictionary.Add("MessageAR", res.MessageAr);
                return res.Status
                    ? Task.FromResult<ActionResult>(Ok(_resDictionary))
                    : Task.FromResult<ActionResult>(BadRequest(_resDictionary));
            }
            catch (Exception ex)
            {
                _resDictionary.Add("Message", ex.Message);
                return Task.FromResult<ActionResult>(BadRequest(_resDictionary));
            }
        }

        [HttpPost("UploadImage")]
        public async Task<IActionResult> UploadImage()
        {
            var type = HttpContext.Request.Form["Type"].ToString();
            var userId = HttpContext.Request.Form["UserId"].ToString();
            var files = HttpContext.Request.Form.Files.ToList();
            var result = await _accounttService.UploadImge(files[0], userId, type);
            _resDictionary.Add("Message", result["Message"]);
            _resDictionary.Add("MessageAR", result["MessageAR"]);
            if (result["Status"])
            {
                return Ok(_resDictionary);
            }
            else
            {
                return BadRequest(_resDictionary);
            }
        }


        [HttpGet("{path?}/Display"), AllowAnonymous]
        public async Task<IActionResult> Display(string name, string path)
        {
            //var accessToken = await HttpContext.GetTokenAsync("access_token");
            //var handler = new JwtSecurityTokenHandler();
            //var jwtSecurityToken = handler.ReadJwtToken(accessToken);
            //var profile = jwtSecurityToken.Payload;
            //var userid=profile["UserId"].ToString()
            var result = await _accounttService.Display(name, path);
            if (result.State)
            {
                return File(result.fileContents, result.contentType);
            }
            else
            {
                _resDictionary.Add("Message", result.Message);
                _resDictionary.Add("MessageAR", result.MessageAR);
                return BadRequest(_resDictionary);
            }
        }

        [HttpGet("CheckCompletionStatus")]
        [Authorize] // Make sure the endpoint requires authentication
        public async Task<IActionResult> CheckCompletionStatus()
        {
            // Get the user ID from the token claims
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(userId))
            {
                _logger.LogError("User ID not found in token");
                return Unauthorized("User ID not found in token");
            }

            var result = await _accounttService.CheckUserCompletionStatus(userId);
            if (result.State)
            {
                return Ok(result.Object);
            }
            else
            {
                _logger.LogError("CheckCompletionStatus Errors", result);
                return BadRequest(result.Message);
            }
        }
    }
}